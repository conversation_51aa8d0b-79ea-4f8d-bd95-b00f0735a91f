{"tailwindCSS.experimental.configFile": "src/renderer/globals.css", "files.associations": {"*.css": "tailwindcss"}, "editor.quickSuggestions": {"strings": "on"}, "editor.codeActionsOnSave": {"source.fixAll.biome": "explicit", "source.organizeImports.biome": "explicit"}, "editor.defaultFormatter": "biomejs.biome", "biome.enabled": true, "editor.formatOnSave": true, "eslint.enable": false, "eslint.validate": [], "i18n-ally.localesPaths": ["src/renderer/i18n/messages"], "i18n-ally.keystyle": "nested", "i18n-ally.displayLanguage": "zh", "i18n-ally.editor.preferEditor": true, "i18n-ally.sourceLanguage": "zh", "i18n-ally.keyPrefix": true, "i18n-ally.defaultNamespace": "translation", "i18n-ally.enabledFrameworks": ["i18next", "react-i18next"], "i18n-ally.namespace": true, "typescript.tsdk": "node_modules\\typescript\\lib", "turboConsoleLog.includeFilename": false, "[typescript]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescriptreact]": {"editor.defaultFormatter": "biomejs.biome"}, "cSpell.words": ["sonner", "triplit"]}