{"chat": {"input-label": "メッセージを入力", "file-not-found": "ファイルが見つかりませんでした", "file-preview-failed": "ファイルのプレビューに失敗しました", "collected": "お気に入り", "input-placeholder": "ここにメッセージを入力してください。Shift+Enterで改行", "send-failed": "メッセージの送信に失敗しました", "tool-bar": {"attach-file": "ファイルを添付(ファイルは10MBを超えることはできません)", "online-search": "オンライン検索", "thinking": "思考", "disabled": "302.AIベンダーのモデルのみ使用可能"}, "model-select-label": "モデルを選択します", "model-select-placeholder": "モデルを選択してください...", "model-select": "モデルを設定", "tab-title": "設定", "model-search-placeholder": "検索...", "no-models-found": "一致するモデルは見つかりませんでした...", "lack-model": "最初にモデルを選択してください！", "lack-provider": "モデルプロバイダーがありません！", "edit-message": "メッセージを編集", "edit-message-only-save": "保存のみ", "function_call": "ツール呼び出し", "reasoning": "推論", "vision": "写真", "file": "ファイル", "music": "音楽", "video": "ビデオ", "support": "サポート"}, "settings": {"about-settings": {"name": "について", "version": "バージョン", "description": {"title": "アプリケーションの説明", "content": "302.AI Studio は、強力なAIチャットアプリケーションで、ユーザーにインテリジェントな対話体験を提供します。複数のAIモデルをサポートし、ファイルアップロード、オンライン検索などの機能を備えており、ユーザーに最高のAIインタラクションプラットフォームを構築することに専念しています。"}, "website": {"title": "公式ウェブサイト"}, "copyright": {"title": "著作権情報", "content": "© 2024 302.AI. 全著作権所有。"}, "license": {"title": "ライセンス", "content": "MIT License"}, "help-center": {"title": "ヘルプセンター"}, "terms-of-service": {"title": "利用規約"}, "privacy-policy": {"title": "プライバシーポリシー"}}, "assistant-settings": {"name": "アシスタント設定"}, "general-settings": {"language": {"label": "言語"}, "name": "一般", "theme": {"dark": "ダーク", "label": "テーマ", "light": "ライト", "system": "システム"}, "privacy-mode": {"title": "プライバシーモード", "description": "自動継承"}, "version-update": {"label": "バージョンの更新", "check-for-updates": "更新を確認", "switch": {"label": "自動更新"}, "version-info": "バージョン", "update-available": "新しいバージョンが検出されました", "update-now": "今すぐ更新", "update-later": "後日更新", "checking": "チェック中", "no-update-available": "現在のバージョンはすでに最新バージョンです", "check-failed": "チェックに失敗しました", "restart-to-update": "即時再起動", "downloading": "ダウンロード", "new-version-available": "新バージョン", "update-downloaded": "新しいバージョンの準備ができました。アプリを再起動してインストールしてください", "new-version-downloaded": "新バージョンが完成"}}, "icon-tooltip": "設定を開く", "model-settings": {"model-list": {"label": "モデルリスト", "no-models-description": "モデルリストは空です...", "current": "現在", "collected": "お気に入り", "search-placeholder": "検索...", "fetch-models": "モデルを取得", "add-model": "モデルを追加", "clear-models": "クリア", "model-name": "モデル名", "model-capabilities": "モデル機能", "model-type": "モデルタイプ", "actions": "操作"}, "add-model-modal": {"title": "モデルを追加", "edit-title": "モデルを編集", "model-id": {"label": "モデルID", "placeholder": "モデルIDを入力してください", "description": "これはモデルの実際の名前で、実際のリクエストに使用されます", "required-error": "モデルIDは空にできません"}, "description": {"label": "備考", "placeholder": "備考を入力してください", "description": "このモデルにより識別しやすい名前を設定できます。表示のみに使用されます"}, "capabilities": {"label": "機能", "reasoning": "推論", "vision": "画像理解", "music": "音楽理解", "video": "ビデオ理解", "function_call": "ツール使用"}, "type": {"label": "モデルタイプ", "language": "言語", "image-generation": "画像生成", "tts": "TTS", "embedding": "Embedding", "rerank": "<PERSON><PERSON>"}, "actions": {"cancel": "キャンセル", "save": "保存", "add-success": "追加に成功しました", "edit-success": "編集に成功しました", "add-error-message": "追加に失敗しました", "edit-error-message": "編集に失敗しました", "delete-error-message": "削除に失敗しました", "delete-success-message": "削除に成功しました", "delete-title": "モデルを削除", "delete-description": "このモデルを削除してもよろしいですか？", "delete-confirm-text": "削除", "clear-title": "すべてのモデルをクリア", "clear-description": "このプロバイダーのすべてのモデルをクリアしてもよろしいですか？この操作は元に戻せません。", "clear-confirm-text": "すべてクリア", "clear-success-message": "すべてのモデルが正常にクリアされました", "clear-error-message": "モデルのクリアに失敗しました"}}, "model-provider": {"add-provider": "モデルを追加", "custom-provider": "カスタムプロバイダー", "add": "追加", "model-check-success": "モデル検証に成功しました", "model-check-failed": "モデル検証に失敗しました", "select-provider": "プロバイダーを選択", "select-provider-description": "左のリストからプロバイダーを選択して設定を構成してください", "add-provider-form": {"check-key": "確認する", "custom-provider": "カスタムプロバイダー", "placeholder": "プロバイダーを選択してください...", "placeholder-2": "APIキーを入力してください...", "placeholder-3": "ベースURLを入力してください...", "provider-select": "プロバイダーを選択", "provider-type": "プロバイダータイプ", "placeholder-1": "サプライヤー名を入力してください...", "provider-name": "カスタム名", "default-name": "カスタムベンダー", "check-key-success": "APIキー検証に正常に！", "check-key-failed": "APIキー検証に失敗しました！", "checking": "検証...", "unverified": "検証されます", "verified": "検証に合格しました", "verification-failed": "認証に失敗しました", "normalized-base-url": "正規化されたベースURL", "full-api-endpoint": "完全なAPIエンドポイントの例", "apply-normalized-url": "正規化されたURLを適用", "url-format-error": "URL形式エラー", "url-empty-error": "URLを空にすることはできません", "url-invalid-format": "無効なURL形式", "verification-required": "プロバイダーを追加する前に、まずAPIキーを確認してください", "verification-required-notice": "💡 通知：プロバイダー設定を追加または保存する前に、API認証が必要です。", "verification-hint": "APIキーの可用性を確認するには、検証ボタンをクリックしてください", "get-api-key": "APIキーを取得するには、ここをクリックしてください", "api-forward": "APIリクエストは次に送信されます", "interface-type": "インターフェースタイプ", "interface-type-placeholder": "インターフェースタイプを選択", "configure": "設定", "icon": "アイコン", "name": "名前", "name-placeholder": "プロバイダー名を入力してください"}, "delete": "削除", "description": "モデル", "not-configured": "未設定", "provider-error": "プロバイダーエラー", "edit": "編集", "label": "モデルプロバイダー", "modal-action": {"add-provider": "プロバイダーを追加", "delete": "プロバイダーを削除", "delete-confirm": "削除", "delete-description": "プロバイダーを削除してもよろしいですか", "delete-description-2": "このプロバイダーが提供するすべてのモデルが削除され、", "delete-description-3": "これらのデフォルトモデルを設定したアシスタントは他のデフォルトモデルに変更されます。", "edit": "設定", "add-provider-confirm": "追加"}, "no-provider-description": "プロバイダーがまだ追加されていません", "edit-provider-form": {"check-key-success": "APIキー検証に正常に！", "check-key-failed": "APIキー検証に失敗しました！", "verification-required": "プロバイダー設定を保存する前に、まずAPIキーを確認してください", "verification-required-notice": "💡 通知：プロバイダー設定を追加または保存する前に、API認証が必要です。", "verification-hint": "APIキーの可用性を確認するには、検証ボタンをクリックしてください"}, "star": "集める"}, "name": "モデル", "loading": "読み込み..."}, "tab-title": "設定", "tool-settings": {"name": "ツール設定"}, "shortcuts-settings": {"name": "ショートカットキー", "title": "キーボードショートカット", "actions": {"send-message": "メッセージ送信", "new-chat": "新しいチャット", "clear-messages": "メッセージクリア", "close-current-tab": "現在のタブを閉じる", "close-other-tabs": "他のタブを閉じる", "delete-current-thread": "現在のスレッドを削除", "open-settings": "設定を開く", "toggle-sidebar": "サイドバー切り替え", "quick-navigation": "クイックナビゲーション", "command-palette": "コマンドパレット", "stop-generation": "生成停止", "new-tab": "新しいタブ", "new-session": "新しいセッション", "regenerate-response": "応答を再生成", "search": "検索", "create-branch": "ブランチ作成", "close-all-tabs": "すべてのタブを閉じる", "restore-last-tab": "最後のタブを復元", "screenshot": "スクリーンショット", "next-tab": "次のタブ", "previous-tab": "前のタブ", "toggle-model-panel": "モデルパネル切り替え", "toggle-incognito-mode": "シークレットモード切り替え", "branch-and-send": "ブランチ作成して送信", "switch-to-tab-1": "タブ1に切り替え", "switch-to-tab-2": "タブ2に切り替え", "switch-to-tab-3": "タブ3に切り替え", "switch-to-tab-4": "タブ4に切り替え", "switch-to-tab-5": "タブ5に切り替え", "switch-to-tab-6": "タブ6に切り替え", "switch-to-tab-7": "タブ7に切り替え", "switch-to-tab-8": "タブ8に切り替え", "switch-to-tab-9": "タブ9に切り替え", "tab-switching-group": "クイックタブ切り替え (1-9)"}, "hints": {"send-message": "チャットでメッセージを送信する方法を選択", "new-chat": "新しい会話を作成するショートカット", "clear-messages": "現在のセッションのメッセージのみクリア、モデル設定などの内容は保持されます", "close-current-tab": "現在アクティブなタブを閉じる", "close-other-tabs": "現在のタブ以外のすべてのタブを閉じる", "delete-current-thread": "現在の会話スレッドを削除", "open-settings": "設定ページを開く", "toggle-sidebar": "サイドバーを表示/非表示", "quick-navigation": "アプリの異なる部分への素早いナビゲーション", "command-palette": "素早い操作のためのコマンドパレットを開く", "stop-generation": "現在のAI応答生成を停止", "new-tab": "新しいチャットタブを作成", "new-session": "現在のタブで新しいセッションを開始", "regenerate-response": "最後のAI応答を再生成", "search": "アプリケーション内で検索", "create-branch": "現在の会話から新しいブランチを作成", "close-all-tabs": "開いているすべてのタブを閉じる", "restore-last-tab": "最後に閉じたタブを復元", "screenshot": "スクリーンショットを撮る（グローバルショートカット）", "next-tab": "次のタブに切り替え", "previous-tab": "前のタブに切り替え", "toggle-model-panel": "モデル選択パネルを表示/非表示", "toggle-incognito-mode": "プライベート会話のためのシークレットモードを切り替え", "branch-and-send": "ブランチを作成して同時にメッセージを送信", "switch-to-tab-1": "タブ1に切り替え", "switch-to-tab-2": "タブ2に切り替え", "switch-to-tab-3": "タブ3に切り替え", "switch-to-tab-4": "タブ4に切り替え", "switch-to-tab-5": "タブ5に切り替え", "switch-to-tab-6": "タブ6に切り替え", "switch-to-tab-7": "タブ7に切り替え", "switch-to-tab-8": "タブ8に切り替え", "switch-to-tab-9": "タブ9に切り替え", "tab-switching-group": "指定のタブに素早く切り替え"}, "description": {"title": "キーボードショートカット説明", "content": "様々なアクションのキーボードショートカットをカスタマイズできます。一部のアクションは302.AIプロバイダーモデルでのみ利用可能です。"}, "recorder": {"placeholder": "クリックしてショートカットを設定", "press-keys": "キーを押してください...", "cancel": "キャンセル", "reset": "リセット", "clear": "クリア", "no-shortcut": "ショートカットなし", "error": {"modifier-required": "ショートカットには少なくとも1つの修飾キー（Ctrl/Cmd/Alt/Shift）が必要です", "shortcut-conflict": "このショートカットは既に存在します。再度録音してください"}}, "scope": {"label": "スコープ", "app": "アプリ内", "global": "グローバル"}}, "text": "設定", "preference-settings": {"name": "好み", "search-provider": {"label": "デフォルトの検索サービス"}, "display-app-store": {"label": "302.AI アプリストア", "switch": {"label": "ホームページにアプリストアを表示"}}, "stream-output": {"label": "ストリーム出力", "smoother": {"label": "ストリームスムーザー", "switch": {"label": "スムーズなストリーミング出力を有効にする"}}, "speed": {"label": "出力速度", "slow": "遅い", "normal": "普通", "fast": "速い"}}, "collapse-code-block": {"label": "チャット設定", "switch": {"hide-code": "デフォルトでコードブロックを折りたたむ", "hide-reason": "推論過程を非表示にする", "collapse-think": "推論過程を自動で折りたたむ", "disable-markdown": "markdownを無効にする"}}, "model-select": {"label": "新会話モデル", "title-model": "会話タイトル生成モデル", "placeholder": "モデルを選択", "search-placeholder": "モデルを検索...", "no-models-found": "モデルが見つかりません", "use-last-model": "前回の会話モデルを使用", "use-current-chat-model": "会話と同じモデルを使用"}}}, "sidebar": {"close-sidebar": {"tooltip": "サイドバーを閉じる"}, "menu-item": {"clean-messages": "メッセージをクリア", "collect-thread": "お気に入り", "generate-title": "タイトルを生成", "delete": "削除", "rename": "名前を変更", "uncollect-thread": "お気に入りを解除", "delete-all": "すべてを削除します"}, "new-thread": {"tooltip": "新しいチャット"}, "open-sidebar": {"tooltip": "サイドバーを開く"}, "search": {"placeholder": "検索..."}, "search-thread": {"tooltip": "会話を検索", "placeholder": "検索"}, "section": {"collected": "お気に入り", "earlier": "以前", "last30Days": "過去30日", "last7Days": "過去7日", "today": "今日", "yesterday": "昨日", "justNow": "ちょうど"}}, "tab-bar": {"menu-item": {"close": "閉じる", "close-all": "すべて閉じる", "reload": "リロード"}}, "thread": {"new-thread-title": "新しい会話", "create-thread-error": "申し訳ありませんが、新しいセッションの作成は失敗しました！", "lack-model": "最初にモデルを選択してください！", "lack-provider": "モデルプロバイダーがありません！", "selected-model-not-found": "最初にモデルを選択してください！", "provider-not-found-for-selected-model": "モデルプロバイダー設定エラー！", "message-not-found": "このメッセージは存在しません！", "failed-to-generate-ai-response": "AI応答の生成に失敗しました！", "private-thread-title": "プライベートチャット"}, "privacy-mode": {"enable-tooltip": "プライバシーモードを有効にする", "disable-tooltip": "プライバシーモードを無効にする", "cannot-toggle-tooltip": "セッション開始後はプライバシーモードを切り替えできません", "cannot-toggle": {"title": "プライバシーモードを切り替えできません", "description": "プライバシーモードはセッション開始前のみ変更できます"}, "enabled": {"title": "プライバシーモードが有効になりました", "description": "このセッションは保存または同期されません"}, "disabled": {"title": "プライバシーモードが無効になりました", "description": "このセッションは正常に保存されます"}, "error": {"title": "プライバシーモードエラー", "description": "プライバシーモードの切り替えに失敗しました"}, "confirm-switch": {"message": "{{action}}しようとしています。これによりプライベートセッションの内容が破棄されます。続行しますか？"}, "confirm-dialog": {"title": "プライバシーモードアクションの確認", "cancel": "キャンセル", "confirm": "続行"}}, "thread-menu": {"actions": {"cancel": "キャンセル", "clean-messages": {"confirmText": "クリア", "description": "メッセージをクリアすると、会話内のすべてのメッセージとファイルが削除されます。続行しますか？", "title": "会話メッセージをクリア"}, "confirm": "確認", "delete": {"confirmText": "削除", "description": "削除後、会話は復元できません。慎重に操作してください。", "title": "この会話を削除してもよろしいですか？"}, "rename": {"confirmText": "確認", "description": "新しい会話名を入力してください。", "edit": {"placeholder": "新しい会話名..."}, "title": "会話名を変更"}, "delete-all": {"title": "すべてのセッションを削除したいですか？", "description": "お気に入りのセッションを除き、他のすべてのセッションは削除され、復元できません。注意してください。", "confirmText": "すべてを削除します"}}}, "common": {"copied-success": "コピーに成功しました", "copied-failed": "コピーが失敗しました", "copy-to-clipboard": "コピー"}, "message-list": {"markdown": {"generating-diagram": "チャートの生成...", "waiting-for-diagram": "チャートの内容を待っています...", "diagram-syntax-error": "グラフ構文エラー"}}, "message": {"copy": "コピー", "copy-success": "コピーに成功しました", "copy-failed": "コピーが失敗しました", "refresh": "再生成", "edit": "編集", "delete": "削除", "thinking": "AIは考えています", "edit-dialog": {"title": "メッセージを編集", "save": "保存", "cancel": "キャンセル", "save-success": "メッセージが正常に更新されました", "save-error": "メッセージの更新に失敗しました"}, "generate-failed": "世代が失敗しました、もう一度やり直してください...", "delete-success": "メッセージが正常に削除されました", "delete-error": "メッセージの削除に失敗しました", "context-menu": {"create-new-branch": "新しいブランチを作成", "create-new-branch-error": "申し訳ありませんが、新しいブランチの作成は失敗しました！", "new-thread-title": "新しい会話", "copy-selected": "選択したテキストをコピー"}, "think": "思考過程", "outputing": "出力中...", "reason": "推論プロセス", "completed": "完了しました"}, "stop-generating": "停止", "new-thread": {"hello-world": "やあ、僕と一緒に世界を探検する準備はできたかい？", "toolbox-label": "AIアプリケーション", "toolbox-button": "より多くのアプリケーション", "toolbox-title": "AIツール", "toolbox-search-placeholder": "検索"}, "attachment": {"file-too-large": "ファイルサイズは10MBを超えることはできません"}, "artifacts": {"preview": "プレビュー", "code": "コード", "lines": "行", "collapse": "折りたたむ", "expand": "展開"}, "shortcuts": {"tab-not-exist": "タブ {{tabNumber}} は存在しません", "tab-switch-error": "タブ {{tabNumber}} への切り替えに失敗しました"}, "toolbox": {"tool-press-error-msg": "302.AIベンダーのAPI Keyが設定されていないか、間違っています", "tool-collection-error-msg": "お気に入りのアプリケーションが失敗しました、もう一度やり直してください", "favorites": "集める"}, "preview": {"zoom-in": "ズームイン", "zoom-out": "ズームアウト", "rotate-left": "左回転", "rotate-right": "右回転", "reset-zoom": "ズームリセット", "download": "ダウンロード", "copy-code": "コードをコピー", "copy-text": "テキストをコピー", "copy-content": "内容をコピー", "toggle-line-numbers": "行番号の切り替え", "open-external": "外部ビューアーで開く", "loading": "読み込み中...", "loading-code": "コードを読み込み中...", "loading-document": "ドキュメントを読み込み中...", "loading-audio": "音声を読み込み中...", "loading-image": "画像を読み込み中...", "loading-text": "テキストを読み込み中...", "copy-failed": "コピーに失敗しました", "copied-to-clipboard": "クリップボードにコピーしました", "content-copied": "コンテンツをコピーしました", "failed-to-load": "読み込みに失敗しました", "failed-to-decode": "デコードに失敗しました", "no-content-available": "利用可能なコンテンツがありません", "document-parsing-unavailable": "ドキュメントの解析ができません。外部ビューアーをご利用ください。", "file-preview-not-supported": "このファイル形式はアプリ内でプレビューできません。", "close": "閉じる", "text-preview": "テキストプレビュー", "code-preview": "コードプレビュー", "text-file": "テキストファイル", "code-file": "コードファイル", "help-text": {"image": "マウスホイールでズーム • ドラッグで移動 • Rキーで回転 • Escキーで閉じる", "audio": "スペースキーで再生/一時停止 • ← →キーでスキップ • Mキーでミュート • Escキーで閉じる", "code": "Ctrl+Cでコピー • Lキーで行番号切り替え • + / -キーでズーム • Escキーで閉じる", "document": "+ / -キーでズーム • 外部リンクをクリックしてシステムビューアーで開く • Escキーで閉じる", "text": "Ctrl+Cでコピー • + / -キーでズーム • Escキーで閉じる"}}}